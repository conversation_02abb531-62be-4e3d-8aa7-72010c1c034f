<?php
// 定义访问常量
define('APP_ACCESS', true);

// 引入必要文件
require_once '../includes/cache.php';
require_once '../includes/config.php';
require_once '../includes/Database.php';
require_once '../includes/Validator.php';
require_once '../includes/GoodsService.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求（用于CORS预检）
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 输出JSON响应
function outputJson($code = 0, $msg = '', $data = []) {
    $response = [
        'code' => $code,
        'msg' => $msg ?: ($code ? 'error' : 'success')
    ];
    
    if (!empty($data)) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit(0);
}

try {
    // 获取请求数据
    $requestData = json_decode(file_get_contents('php://input'), true);
    
    // 如果是POST请求但没有数据，尝试从POST参数获取
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && empty($requestData)) {
        $requestData = $_POST;
    }
    
    // 如果是GET请求，从查询字符串获取数据
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $requestData = $_GET;
    }
    
    // 验证请求数据
    if (empty($requestData) || (!isset($requestData['id']) && !isset($requestData['item_no']))) {
        outputJson(400, '请求参数错误，缺少商品号');
    }
    
    // 获取商品号
    $itemNumber = $requestData['id'] ?? $requestData['item_no'] ?? '';
    
    // 检查缓存
    $cacheKey = "goods_query_" . md5($itemNumber);
    if (ENABLE_CACHE) {
        $cached = Cache::get($cacheKey);
        if ($cached !== null) {
            outputJson(0, 'success', $cached);
        }
    }
    
    // 创建服务实例并查询商品
    $goodsService = new GoodsService();
    $result = $goodsService->findGoodsByItemNumber($itemNumber);

    Cache::set($cacheKey,$result['data']);
    // 输出结果
    outputJson($result['code'], $result['msg'], $result['data'] ?? []);
    
} catch (PDOException $e) {
    // 处理PDO异常
    if (DEBUG_MODE) {
        outputJson(500, '数据库错误: ' . $e->getMessage());
    } else {
        outputJson(500, '数据库操作失败');
    }
} catch (Exception $e) {
    // 处理其他异常
    if (DEBUG_MODE) {
        outputJson(500, $e->getMessage());
    } else {
        outputJson(500, '服务器内部错误');
    }
}
?>
