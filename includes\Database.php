<?php
// 防止直接访问
if (!defined('APP_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * 数据库连接和操作类 (PDO版本)
 */
class Database {
    private $host;
    private $user;
    private $pass;
    private $dbname;
    private $conn;
    private static $instance = null;

    /**
     * 构造函数，初始化数据库连接参数
     */
    private function __construct() {
        $this->host = DB_HOST;
        $this->user = DB_USER;
        $this->pass = DB_PASS;
        $this->dbname = DB_NAME;
        $this->connect();
    }

    /**
     * 获取数据库实例（单例模式）
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new Database();
        }
        return self::$instance;
    }

    /**
     * 连接数据库
     */
    private function connect() {
        try {
            // 构建DSN
            $dsn = "sqlsrv:Server={$this->host};Database={$this->dbname}";
            
            // 设置PDO选项
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::SQLSRV_ATTR_ENCODING => PDO::SQLSRV_ENCODING_UTF8
            ];
            
            // 创建PDO实例
            $this->conn = new PDO($dsn, $this->user, $this->pass, $options);
        } catch (PDOException $e) {
            $this->handleError($e->getMessage());
        }
    }

    /**
     * 执行预处理语句查询
     * @param string $sql SQL语句
     * @param array $params 参数数组
     * @return array 查询结果
     */
    public function query($sql, $params = []) {
        try {
            // 准备语句
            $stmt = $this->conn->prepare($sql);
            
            // 执行查询
            $stmt->execute($params);
            
            // 获取结果
            $results = $stmt->fetchAll();
            
            // 处理结果编码
            return $this->convertEncoding($results);
        } catch (PDOException $e) {
            $this->handleError($e->getMessage());
            return [];
        }
    }

    /**
     * 执行单条记录查询
     * @param string $sql SQL语句
     * @param array $params 参数数组
     * @return array|null 查询结果
     */
    public function queryOne($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetch();
            
            return $result ? $this->convertEncoding($result) : null;
        } catch (PDOException $e) {
            $this->handleError($e->getMessage());
            return null;
        }
    }

    /**
     * 执行插入、更新或删除操作
     * @param string $sql SQL语句
     * @param array $params 参数数组
     * @return int 受影响的行数
     */
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->handleError($e->getMessage());
            return 0;
        }
    }

    /**
     * 获取最后插入的ID
     * @return string 最后插入的ID
     */
    public function lastInsertId() {
        return $this->conn->lastInsertId();
    }

    /**
     * 开始事务
     */
    public function beginTransaction() {
        $this->conn->beginTransaction();
    }

    /**
     * 提交事务
     */
    public function commit() {
        $this->conn->commit();
    }

    /**
     * 回滚事务
     */
    public function rollback() {
        $this->conn->rollBack();
    }

    /**
     * 转换数组编码
     * @param array $arr 需要转换的数组
     * @return array 转换后的数组
     */
    private function convertEncoding($arr) {
        if (!is_array($arr)) {
            return $arr;
        }
        
        $result = [];
        foreach ($arr as $key => $value) {
            if (is_array($value)) {
                $result[$key] = $this->convertEncoding($value);
            } else if (is_string($value)) {
                // PDO应该已经处理了编码，但为了保险起见，我们确保所有字符串都是UTF-8编码
                $result[$key] = $value;
            } else {
                $result[$key] = $value;
            }
        }
        return $result;
    }

    /**
     * 处理错误
     * @param string $message 错误信息
     */
    private function handleError($message) {
        if (DEBUG_MODE) {
            echo json_encode(['code' => 500, 'msg' => $message]);
        } else {
            echo json_encode(['code' => 500, 'msg' => '数据库操作失败']);
        }
        exit;
    }

    /**
     * 关闭数据库连接
     */
    public function close() {
        $this->conn = null;
    }

    /**
     * 析构函数，确保关闭数据库连接
     */
    public function __destruct() {
        $this->close();
    }
}
?>
