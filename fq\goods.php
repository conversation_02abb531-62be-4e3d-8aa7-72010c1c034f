<?php

function OpenConn($database){
    static $conn = null;
	if ($conn !== null) {
        return $conn;
    }
	try{
		$conn = new PDO("sqlsrv:server=(local);Database=".$database);
		$conn->setAttribute( PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION );
        $conn->setAttribute( PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC );
	} catch(PDOException $e) {
		die( "Connection could not be established - " . $e->getMessage() );
	}
	return $conn;
}

function openConn2($server,$database,$uid,$pwd){  
    try {
        $connOptions = array("Database"=>$database,  
            "Uid"=>$uid, "PWD"=>$pwd);  
        $conn = sqlsrv_connect($server, $connOptions);  
        if($conn) {
            return $conn;
        }else{
            return false;
        }
    }
    catch(Exception $e){  
        return false;  
    }
}

function arrayIconv($arr, $in_charset = "gbk", $out_charset = "utf-8") {
    return eval('return ' . iconv($in_charset, $out_charset, var_export($arr, true)) . ';');
}

function getData($conn,$sql){
	$data = [];
	$stmt = $conn->query($sql);
	foreach ($stmt->fetchAll() as $row){
		array_push($data,$row);
	}
    return $data;
}

function getData2($conn,$sql){
	$sql = iconv('UTF-8','GBK',$sql);
    $result = sqlsrv_query($conn,$sql);
    $data = array();
    while ($rows = sqlsrv_fetch_array($result,SQLSRV_FETCH_ASSOC)){
        $rows = arrayIconv($rows);
        array_push($data,$rows);
    }
    return $data;
}

$data = json_decode(file_get_contents('php://input'));
if (!property_exists($data,'id'))outJson(-1,'error,illegal invocation.');
$id = $data -> id;
$sql = <<<EOT
SELECT DISTINCT rtrim(A.[Goods_no]) as Goods_no,
B.Size_class,
B.Goods_name,
b.Brand,
b.[Year],
b.Season,
b.Category,
b.Range,
b.UnitPrice,
b.Item,
c.ableChg,
STUFF((
   SELECT ','+RTRIM([ColorID])+'/'+[Color]
      FROM [dbo].[vGoodsColor]
       WHERE Goods_No = a.goods_no
        FOR XML PATH('')),1,1,''
)AS Colors
FROM [dbo].[vGoodsColor] AS A
LEFT JOIN Goods  B ON a.Goods_No=b.Goods_no
LEFT JOIN (
SELECT
	goods_no,
	COUNT ( goods_no ) AS ableChg 
FROM
	PureceiptGoods 
WHERE
	goods_no LIKE '%$id%' 
GROUP BY
	Goods_no
) as C on a.goods_no = c.goods_no
WHERE b.Goods_no like '%$id%'
EOT;
$conn = OpenConn2("**************,1433","regdatas","sa","Tgg8393");
if($conn == false)outJson(-2,'error,database connect failed.');
$result = getData2($conn,$sql);
//sqlsrv_close($conn);
outJson(0,'success',$result);
?>