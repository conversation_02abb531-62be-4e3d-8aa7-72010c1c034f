<?php
// 防止直接访问
if (!defined('APP_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * 输入验证类
 */
class Validator {
    /**
     * 验证商品ID
     * @param string $itemNumber 商品ID
     * @return bool 验证结果
     */
    public function validateItemNumber($itemNumber) {
        // 允许字母、数字、连字符和百分号（用于模糊查询）
        return preg_match('/^[a-zA-Z0-9\-%]+$/', $itemNumber);
    }
    
    /**
     * 过滤输入字符串，防止XSS攻击
     * @param string $input 输入字符串
     * @return string 过滤后的字符串
     */
    public function sanitizeInput($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * 验证并过滤输入
     * @param string $input 输入字符串
     * @return array 验证结果和过滤后的字符串
     */
    public function validateAndSanitize($input) {
        $sanitized = $this->sanitizeInput($input);
        $isValid = $this->validateItemNumber($sanitized);
        
        return [
            'isValid' => $isValid,
            'value' => $sanitized
        ];
    }
}
?>