:root {
  --primary-color: #2196F3;
  --secondary-color: #0D47A1;
  --text-color: #333;
  --light-gray: #f5f5f5;
  --border-color: #ddd;
  --success-color: #4CAF50;
  --error-color: #F44336;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--light-gray);
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.search-container {
  display: flex;
  margin-bottom: 20px;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.3s;
}

.search-input:focus {
  border-color: var(--primary-color);
}

.search-button {
  margin-left: 10px;
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.search-button:hover {
  background-color: var(--secondary-color);
}

.results-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.result-item {
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:nth-child(even) {
  background-color: var(--light-gray);
}

.copy-button {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 5px 10px;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.copy-button:hover {
  background-color: var(--light-gray);
}

.copy-button.copied {
  background-color: var(--success-color);
  color: white;
}

.loading {
  text-align: center;
  padding: 20px;
}

.error-message {
  color: var(--error-color);
  padding: 15px;
  text-align: center;
}

@media (max-width: 600px) {
  .search-container {
    flex-direction: column;
  }
  
  .search-button {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }
}