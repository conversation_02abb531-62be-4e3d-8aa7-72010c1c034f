mui('body').on('tap', '.mui-btn', function() {
    var id = $('.data').val();
    if (!id || id.trim() == "") {
        mui.alert("货号不能为空!");
    } else {
        mui.ajax('goods.php', {
            type: 'POST',
            dataType: 'json',
            headers: {
                'Content-type': 'application/json;charset=utf-8'
            },
            data: JSON.stringify({id: id}),
            success: function(result) {
                var str = '';
                if (typeof(result.data) != 'object') {
                    $(".detail").html("未能查询到相关货品信息!");
                    return;
                }
                $.each(result.data, function(i, item) {
                    str += `<div class="detail">
                    <div class="info" id="info">
                    货号：${item.Goods_no}<br>
                    品名：${item.Goods_name}<br>
                    品牌：${item.Brand}<br>
                    年份：${item.Year}<br>
                    季节：${item.Season}<br>
                    类别：${item.Category}<br>
                    尺码：${item.Size_class}<br>
                    系列：${item.Range}<br>
                    价格：${item.UnitPrice}<br>
                    仓位：${item.Item}<br>
                    颜色：${item.Colors}<br>
                    ${ableChg(item.ableChg)}
                    </div>
                    <div class="copy">copy</div></div>`
                });
                $(".body").html(str);
                $(".detail:even").css("background-color", "#ddd");
                $(function() {
                    $('.copy').click(function() {
                        navigator.clipboard.writeText($(this).prev().text()).then(function() {
                            mui.toast('Text copied to clipboard');
                        }, function(err) {
                            mui.toast('Error in copying text: ', err);
                        });
                    });
                });
            }
        });
    }
});
function copyToClipboard(elementId) {
      const element = document.getElementById(elementId);
      element.select();
      document.execCommand('copy');
      element.blur();

      const btn = element.nextElementSibling;
      const span = btn.querySelector('span');
      const originalText = span.textContent;

      btn.classList.add('copied');
      span.textContent = '已复制';

      setTimeout(() => {
        btn.classList.remove('copied');
        span.textContent = originalText;
      }, 1000);
    }
function ableChg($i) {
    if ($i == null) {
        return '<b style="color:blue">该款式允许修改款号或颜色</b>'
    } else {
        return '<b style="color:red">该款式禁止修改款号或颜色</b>'
    }
}