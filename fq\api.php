<?php
include "sqlsrv.php";
include "Validator.php";
function outJson($code = 0, $msg = '', $data = []){
    $out['code'] = $code ?: 0;
    $out['msg'] = $msg ?: ($out['code'] ? 'error' : 'success');
    if (!empty($data))$out['data'] = $data;
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($out, JSON_UNESCAPED_UNICODE);
    exit(0);
}

function arrayIconv($arr) {
    $ret = array_map(function($arr){
		if (is_array($arr)){
			return $this->arrayIconv($arr);
		}else{
			return mb_convert_encoding($arr,"UTF-8","GBK");
		}
	},$arr);
	return $ret;
}

function arrayIconv2($arr, $in_charset = "gbk", $out_charset = "utf-8") {
    return eval('return ' . iconv($in_charset, $out_charset, var_export($arr, true)) . ';');
}
$_db = [
	"hostname" => "**************,1433",
	"username" => "sa",
	"password" => "Tgg8393",
	"dbname"   => "regdatas"
];

$db = SQLSrv::getdatabase($_db);

$validator = new Validator();

// Assuming item number is passed via GET parameter 'item_no'
$itemNumber = $_GET['item_no'] ?? '';

if (!$validator->validateItemNumber($itemNumber)) {
    outJson(1, 'Invalid item number format.');
}

// Use parameter binding with prepared statement
$sql = "select rtrim(goods_no) goods_no,goods_name,brand,[year],season,item from goods where goods_no like ?";
$params = ['%' . $itemNumber . '%']; // Add wildcards for LIKE query

$ret = $db->findAllPrepared($sql, $params);

$data = [];
foreach ($ret as $row){
	array_push($data,arrayIconv($row));
}
outJson(0,"success",$data);
?>