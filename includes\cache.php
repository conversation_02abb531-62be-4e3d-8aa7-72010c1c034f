<?php
/**
 * 简单文件缓存类
 * 支持TTL过期机制
 */
class Cache {
    private static $cacheDir = '../cache/';
    private static $defaultTTL = 600; // 1小时默认过期时间
    
    /**
     * 初始化缓存目录
     */
    public static function init() {
        if (!is_dir(self::$cacheDir)) {
            mkdir(self::$cacheDir, 0755, true);
        }
    }
    
    /**
     * 设置缓存
     * @param string $key 缓存键
     * @param mixed $data 缓存数据
     * @param int $ttl 过期时间（秒）
     * @return bool
     */
    public static function set($key, $data, $ttl = null) {
        self::init();
        $ttl = $ttl ?: self::$defaultTTL;
        $expireTime = time() + $ttl;
        
        $cacheData = [
            'expire' => $expireTime,
            'data' => $data
        ];
        
        $filename = self::$cacheDir . md5($key) . '.cache';
        return file_put_contents($filename, serialize($cacheData)) !== false;
    }
    
    /**
     * 获取缓存
     * @param string $key 缓存键
     * @return mixed|null
     */
    public static function get($key) {
        $filename = self::$cacheDir . md5($key) . '.cache';
        
        if (!file_exists($filename)) {
            return null;
        }
        
        $content = file_get_contents($filename);
        if ($content === false) {
            return null;
        }
        
        $cacheData = unserialize($content);
        if (!$cacheData || !isset($cacheData['expire'])) {
            return null;
        }
        
        // 检查是否过期
        if (time() > $cacheData['expire']) {
            self::delete($key);
            return null;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * 删除缓存
     * @param string $key 缓存键
     * @return bool
     */
    public static function delete($key) {
        $filename = self::$cacheDir . md5($key) . '.cache';
        if (file_exists($filename)) {
            return unlink($filename);
        }
        return true;
    }
    
    /**
     * 清空所有缓存
     * @return bool
     */
    public static function clear() {
        if (!is_dir(self::$cacheDir)) {
            return true;
        }
        
        $files = glob(self::$cacheDir . '*.cache');
        foreach ($files as $file) {
            unlink($file);
        }
        return true;
    }
    
    /**
     * 检查缓存是否存在且未过期
     * @param string $key 缓存键
     * @return bool
     */
    public static function exists($key) {
        return self::get($key) !== null;
    }
}
?>