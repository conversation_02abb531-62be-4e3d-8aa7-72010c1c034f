/**
 * 商品查询应用
 */
class GoodsApp {
  /**
   * 构造函数
   */
  constructor() {
    // DOM元素
    this.searchInput = document.getElementById('searchInput');
    this.searchButton = document.getElementById('searchButton');
    this.resultsContainer = document.getElementById('resultsContainer');
    
    // 绑定事件处理器
    this.bindEvents();
  }
  
  /**
   * 绑定事件
   */
  bindEvents() {
    // 搜索按钮点击事件
    this.searchButton.addEventListener('click', () => this.handleSearch());
    
    // 输入框回车事件
    this.searchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.handleSearch();
      }
    });
    
    // 委托处理复制按钮点击事件
    this.resultsContainer.addEventListener('click', (e) => {
      if (e.target.classList.contains('copy-button')) {
        this.handleCopy(e.target);
      }
    });
  }
  
  /**
   * 处理搜索
   */
  async handleSearch() {
    const itemNumber = this.searchInput.value.trim();
    
    if (!itemNumber) {
      this.showError('请输入商品号');
      return;
    }
    
    this.showLoading();
    
    try {
      const result = await ApiService.searchGoods(itemNumber);
      
      if (result.code === 0 && result.data) {
        this.renderResults(result.data);
      } else {
        this.showError(result.msg || '查询失败');
      }
    } catch (error) {
      console.error('搜索处理错误:', error);
      this.showError('系统错误，请稍后重试');
    }
  }
  
  /**
   * 渲染结果
   * @param {Array} data - 结果数据
   */
  renderResults(data) {
    if (!data.length) {
      this.showError('未找到相关商品信息');
      return;
    }
    
    let html = '';
    
    data.forEach((item, index) => {
      html += `
        <div class="result-item" data-index="${index}">
          <div class="result-content">
            <p><strong>货号：</strong>${item.Goods_no || ''}</p>
            <p><strong>品名：</strong>${item.Goods_name || ''}</p>
            <p><strong>品牌：</strong>${item.Brand || ''}</p>
            <p><strong>年份：</strong>${item.Year || ''}</p>
            <p><strong>季节：</strong>${item.Season || ''}</p>
            <p><strong>类别：</strong>${item.Category || ''}</p>
            <p><strong>尺码：</strong>${item.Size_class || ''}</p>
            <p><strong>系列：</strong>${item.Range || ''}</p>
            <p><strong>价格：</strong>${item.UnitPrice || ''}</p>
            <p><strong>仓位：</strong>${item.Item || ''}</p>
            <p><strong>颜色：</strong>${item.Colors || ''}</p>
            <p><strong>${this.getChangeStatus(item.ableChg)}</strong></p>
          </div>
          <button class="copy-button" data-index="${index}">复制</button>
        </div>
      `;
    });
    
    this.resultsContainer.innerHTML = html;
    
    // 保存数据到实例，用于复制功能
    this.resultData = data;
  }
  
  /**
   * 获取修改状态文本
   * @param {string|null} ableChg - 是否可修改
   * @returns {string} - 状态文本
   */
  getChangeStatus(ableChg) {
    if (ableChg === null) {
      return '<span style="color:blue">该款式允许修改款号或颜色</span>';
    } else {
      return '<span style="color:red">该款式禁止修改款号或颜色</span>';
    }
  }
  
  /**
   * 处理复制
   * @param {HTMLElement} button - 复制按钮
   */
  handleCopy(button) {
    const index = button.dataset.index;
    const item = this.resultData[index];
    
    if (!item) return;
    
    // 构建要复制的文本
    const text = `
货号：${item.Goods_no || ''}
品名：${item.Goods_name || ''}
品牌：${item.Brand || ''}
年份：${item.Year || ''}
季节：${item.Season || ''}
类别：${item.Category || ''}
尺码：${item.Size_class || ''}
系列：${item.Range || ''}
价格：${item.UnitPrice || ''}
仓位：${item.Item || ''}
颜色：${item.Colors || ''}
${item.ableChg === null ? '该款式允许修改款号或颜色' : '该款式禁止修改款号或颜色'}
    `.trim();
    
    // 复制到剪贴板
    navigator.clipboard.writeText(text)
      .then(() => {
        // 显示复制成功状态
        button.textContent = '已复制';
        button.classList.add('copied');
        
        // 2秒后恢复原状
        setTimeout(() => {
          button.textContent = '复制';
          button.classList.remove('copied');
        }, 2000);
      })
      .catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制');
      });
  }
  
  /**
   * 显示加载中
   */
  showLoading() {
    this.resultsContainer.innerHTML = '<div class="loading">正在加载数据...</div>';
  }
  
  /**
   * 显示错误信息
   * @param {string} message - 错误信息
   */
  showError(message) {
    this.resultsContainer.innerHTML = `<div class="error-message">${message}</div>`;
  }
}

/**
 * 当DOM加载完成后初始化应用
 */
document.addEventListener('DOMContentLoaded', () => {
  // 创建应用实例
  const app = new GoodsApp();
});