<?php
// 防止直接访问
if (!defined('APP_ACCESS')) {
    die('Direct access not permitted');
}

// 数据库配置
define('DB_HOST', '**************,1433');
define('DB_USER', 'sa');
define('DB_PASS', 'Tgg8393');
define('DB_NAME', 'regdatas');

// 应用配置
define('APP_NAME', '商品资料查询系统');
define('APP_VERSION', '2.0.0');
define('DEBUG_MODE', false);
define('ENABLE_CACHE', true);

// 错误处理
if (DEBUG_MODE) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    error_reporting(0);
}

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 字符编码
ini_set('default_charset', 'UTF-8');
?>