/**
 * API服务模块
 */
const ApiService = {
  /**
   * 查询商品信息
   * @param {string} itemNumber - 商品号
   * @returns {Promise} - 返回Promise对象
   */
  async searchGoods(itemNumber) {
    try {
      const response = await axios.post('api/goods.php', {
        id: itemNumber
      });
      
      return response.data;
    } catch (error) {
      console.error('API请求错误:', error);
      
      // 返回统一的错误格式
      return {
        code: error.response?.status || 500,
        msg: error.response?.data?.msg || '网络请求失败，请稍后重试'
      };
    }
  }
};