<?php
// 防止直接访问
if (!defined('APP_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * 商品服务类
 */
class GoodsService {
    private $db;
    private $validator;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->db = Database::getInstance();
        $this->validator = new Validator();
    }
    
    /**
     * 根据商品号查询商品信息
     * @param string $itemNumber 商品号
     * @return array 查询结果
     */
    public function findGoodsByItemNumber($itemNumber) {
        // 验证并过滤输入
        $validated = $this->validator->validateAndSanitize($itemNumber);
        
        if (!$validated['isValid']) {
            return [
                'code' => 400,
                'msg' => '无效的商品款号格式'
            ];
        }
        
        $itemNumber = $validated['value'];
        $likeParam = "%$itemNumber%";
        
        // 构建SQL查询
        $sql = <<<EOT
SELECT DISTINCT 
    rtrim(A.[Goods_no]) as Goods_no,
    B.<PERSON>ze_class,
    B.Goods_name,
    B.Brand,
    B.[Year],
    B.Season,
    B.Category,
    B.Range,
    B.UnitPrice,
    B.Item,
    C.ableChg,
    STUFF((
        SELECT ',' + RTRIM([ColorID]) + '/' + [Color]
        FROM [dbo].[vGoodsColor]
        WHERE Goods_No = A.goods_no
        FOR XML PATH('')
    ), 1, 1, '') AS Colors
FROM [dbo].[vGoodsColor] AS A
LEFT JOIN Goods B ON A.Goods_No = B.Goods_no
LEFT JOIN (
    SELECT
        goods_no,
        COUNT(goods_no) AS ableChg 
    FROM
        PureceiptGoods 
    WHERE
        goods_no LIKE ?
    GROUP BY
        Goods_no
) AS C ON A.goods_no = C.goods_no
WHERE B.Goods_no LIKE ?
EOT;
        
        // 执行查询 - 使用参数绑定
        $params = [$likeParam, $likeParam];
        $results = $this->db->query($sql, $params);
        
        if (empty($results)) {
            return [
                'code' => 404,
                'msg' => '未找到相关商品信息'
            ];
        }
        
        return [
            'code' => 0,
            'msg' => 'success',
            'data' => $results
        ];
    }
}
?>
